// Test script to verify environment variables are loaded correctly
const fs = require('fs');
const path = require('path');

console.log('Testing environment variable loading...');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
  console.log('✓ .env file exists');
  const envContent = fs.readFileSync(envPath, 'utf8');
  console.log('Environment file content:');
  console.log(envContent);
} else {
  console.log('✗ .env file not found');
}

// Check babel config
const babelConfigPath = path.join(__dirname, 'babel.config.js');
if (fs.existsSync(babelConfigPath)) {
  console.log('✓ babel.config.js exists');
  const babelConfig = require(babelConfigPath);
  console.log('Babel config:', JSON.stringify(babelConfig, null, 2));
} else {
  console.log('✗ babel.config.js not found');
}
