{"$schema": "https://turbo.build/schema.json", "pipeline": {"build:android": {"env": ["ORG_GRADLE_PROJECT_newArchEnabled"], "inputs": ["package.json", "android", "!android/build", "src/*.ts", "src/*.tsx", "example/package.json", "example/android", "!example/android/.gradle", "!example/android/build", "!example/android/app/build"], "outputs": []}, "build:ios": {"env": ["RCT_NEW_ARCH_ENABLED"], "inputs": ["package.json", "*.podsp<PERSON>", "ios", "src/*.ts", "src/*.tsx", "example/package.json", "example/ios", "!example/ios/build", "!example/ios/Pods"], "outputs": []}}}