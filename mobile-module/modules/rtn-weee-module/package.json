{"name": "rtn-weee-module", "version": "0.0.0", "description": "weee native module", "main": "src/index", "codegenConfig": {"name": "RTNWeeeModuleSpec", "type": "modules", "jsSrcsDir": "src", "android": {"javaPackageName": "com.sayweee.react"}}, "author": " <> ()", "license": "UNLICENSED", "homepage": "#readme", "devDependencies": {"@react-native/babel-preset": "0.79.2", "@types/react": "^19.0.0", "react": "19.0.0", "react-native": "0.79.2"}, "create-react-native-library": {"languages": "kotlin-objc", "nativePackageId": "com.sayweee.react", "packageName": "com.sayweee.react", "type": "turbo-module", "version": "0.51.1"}}