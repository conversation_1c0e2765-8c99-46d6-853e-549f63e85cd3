import type { TurboModule } from 'react-native';
import { TurboModuleRegistry } from 'react-native';


// 响应类型
export interface WeeeResponse {
  result: boolean;
  message: string;
  message_id: string;
  data: string;
}

export interface Spec extends TurboModule {

  get(url: string, params?: Object): WeeeResponse;
  post(url: string, params?: Object): WeeeResponse;
  post_batch(url: string, params: Object[]): WeeeResponse;
  put(url: string, params?: Object): WeeeResponse;
  put_batch(url: string, params?: Object[]): WeeeResponse;
  // delete method
  del(url: string): WeeeResponse;

  //addListener(eventName: string): void;
  //removeListeners(count: number): void;   

}

export default TurboModuleRegistry.getEnforcing<Spec>('RTNWeeeNetwork');
