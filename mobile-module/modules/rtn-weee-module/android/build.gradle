buildscript {
  ext.getExtOrDefault = {name ->
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['RTNWeeeNetwork_' + name]
  }

  repositories {
    google()
    mavenCentral()
  }

  dependencies {
    classpath "com.android.tools.build:gradle:8.7.2"
    // noinspection DifferentKotlinGradleVersion
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${getExtOrDefault('kotlinVersion')}"
  }
}


apply plugin: "com.android.library"
apply plugin: "kotlin-android"

apply plugin: "com.facebook.react"

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties["RTNWeeeNetwork_" + name]).toInteger()
}

android {
  namespace "com.sayweee.react"

  compileSdkVersion getExtOrIntegerDefault("compileSdkVersion")

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault("minSdkVersion")
    targetSdkVersion getExtOrIntegerDefault("targetSdkVersion")
  }

  buildFeatures {
    buildConfig true
  }

  buildTypes {
    release {
      minifyEnabled false
    }
  }

  lintOptions {
    disable "GradleCompatible"
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  sourceSets {
    main {
      java.srcDirs += [
        "generated/java",
        "generated/jni"
      ]
    }
  }
}

repositories {
  mavenCentral()
  google()
}

def kotlin_version = getExtOrDefault("kotlinVersion")

dependencies {
  implementation "com.facebook.react:react-android"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

  // weee lib
  implementation 'com.sayweee.libs:wrapper-http:0.0.3-SNAPSHOT'
  implementation 'com.sayweee.libs:http-extension:0.0.4-SNAPSHOT'

  // weee module
  implementation 'com.sayweee.module:core-order:0.1.10-SNAPSHOT'

}

react {
  jsRootDir = file("../src/")
  libraryName = "RTNWeeeModule"
  codegenJavaPackageName = "com.sayweee.react"
}
