package com.sayweee.react

import com.facebook.react.BaseReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.model.ReactModuleInfo
import com.facebook.react.module.model.ReactModuleInfoProvider
import com.sayweee.react.cache.RTNWeeeCacheModule
import com.sayweee.react.network.RTNWeeeNetworkModule
import com.sayweee.react.route.RTNWeeeRouteModule
import java.util.HashMap

class RTNWeeeModulePackage : BaseReactPackage() {

  override fun getModule(
    name: String,
    reactContext: ReactApplicationContext): NativeModule? {
    return if (name == RTNWeeeNetworkModule.NAME) {
      RTNWeeeNetworkModule(reactContext)
    } else if (name == RTNWeeeCacheModule.NAME) {
      RTNWeeeCacheModule(reactContext)
    } else if (RTNWeeeRouteModule.NAME == name) {
      RTNWeeeRouteModule(reactContext)
    } else {
      null
    }
  }

  override fun getReactModuleInfoProvider(): ReactModuleInfoProvider {
    return ReactModuleInfoProvider {
      val moduleInfos: MutableMap<String, ReactModuleInfo> = HashMap()
      addModule(moduleInfos, RTNWeeeNetworkModule.NAME)
      addModule(moduleInfos, RTNWeeeCacheModule.NAME)
      addModule(moduleInfos, RTNWeeeRouteModule.NAME)
      moduleInfos
    }
  }

  private fun addModule(moduleInfos: MutableMap<String, ReactModuleInfo>, name: String) {
    moduleInfos[name] = ReactModuleInfo(
      name,
      name,
      false,  // canOverrideExistingModule
      false,  // needsEagerInit
      false,  // isCxxModule
      true // isTurboModule
    )
  }
}
