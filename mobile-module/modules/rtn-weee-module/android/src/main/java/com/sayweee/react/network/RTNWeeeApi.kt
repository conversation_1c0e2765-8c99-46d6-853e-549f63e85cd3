package com.sayweee.react.network

import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.WritableMap
import com.sayweee.wrapper.bean.ResponseBean
import com.sayweee.wrapper.bean.SimpleResponseBean
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.QueryMap
import retrofit2.http.Url
import java.io.Serializable


//
// Created by <PERSON><PERSON> on 10/07/2025.
//
interface RTNWeeeApi {

    @GET
    fun get(
        @Url url: String?, @QueryMap params: HashMap<String, Any?>?
    ): Call<SimpleResponseBean>?

    @POST
    fun post(
        @Url url: String?, @Body params: HashMap<String, Any?>?
    ): Call<SimpleResponseBean>?

    @POST
    fun post(
        @Url url: String?, @Body params: ArrayList<Any>?
    ): Call<SimpleResponseBean>?

    @PUT
    fun put(
        @Url url: String?, @Body params: HashMap<String, Any?>?
    ): Call<SimpleResponseBean>?

    @PUT
    fun put(
        @Url url: String?, @Body params: ArrayList<Any>?
    ): Call<SimpleResponseBean>?

    @DELETE
    fun delete(
        @Url url: String?
    ): Call<SimpleResponseBean>?

}