package com.sayweee.react.route

import com.facebook.react.bridge.ReactApplicationContext
import com.sayweee.react.NativeRTNWeeeRouteSpec


//
// Created by <PERSON><PERSON> on 16/07/2025.
//
class RTNWeeeRouteModule(private val reactContext: ReactApplicationContext): NativeRTNWeeeRouteSpec(reactContext) {

    companion object {
        const val NAME = "RTNWeeeRoute"
    }

    override fun getName(): String {
        return NAME
    }

    override fun push(url: String?) {
        TODO("Not yet implemented")
    }

    override fun pop() {
        if (reactContext.currentActivity != null) {
            reactContext.currentActivity?.finish()
        }
    }
}