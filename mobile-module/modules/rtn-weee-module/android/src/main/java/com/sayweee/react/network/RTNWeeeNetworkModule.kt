package com.sayweee.react.network


import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.ReadableType
import com.facebook.react.bridge.WritableMap
import com.facebook.react.module.annotations.ReactModule
import com.sayweee.react.NativeRTNWeeeNetworkSpec
import com.sayweee.wrapper.bean.SimpleResponseBean
import com.sayweee.wrapper.http.RetrofitIml


@ReactModule(name = RTNWeeeNetworkModule.NAME)
class RTNWeeeNetworkModule(reactContext: ReactApplicationContext) :
    NativeRTNWeeeNetworkSpec(reactContext) {

    companion object {
        const val NAME = "RTNWeeeNetwork"
    }

    override fun getName(): String {
        return NAME
    }

    override fun get(url: String?, params: ReadableMap?): WritableMap {
        val response = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .get(url, convertMap(params))?.execute();
        val body = response?.body()
        //response?.code() == 200
        return handleResult(body)
    }

    override fun post(url: String?, params: ReadableMap?): WritableMap {
        val response = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .post(url, convertMap(params))?.execute();
        val body = response?.body()
        return handleResult(body)
    }

    override fun post_batch(url: String?, params: ReadableArray?): WritableMap {
        val response = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .post(url, convertArray(params))?.execute();
        val body = response?.body()
        return handleResult(body)
    }

    override fun put(url: String?, params: ReadableMap?): WritableMap {
        val response = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .put(url, convertMap(params))?.execute();
        val body = response?.body()
        return handleResult(body)
    }

    override fun put_batch(url: String?, params: ReadableArray?): WritableMap {
        val response = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .put(url, convertArray(params))?.execute();
        val body = response?.body()
        return handleResult(body)
    }

    override fun del(url: String?): WritableMap {
        val response = RetrofitIml.get().getHttpService(RTNWeeeApi::class.java)
            .delete(url)?.execute();
        val body = response?.body()
        return handleResult(body)
    }


    private fun handleResult(body: SimpleResponseBean?): WritableMap {
        val result = Arguments.createMap()
        body?.let {
            result.putBoolean("result", body.result)
            result.putString("message", body.message)
            result.putString("message_id", body.message_id)
            result.putString("data", body.data)
        }
        return result
    }

    private fun convertMap(readableMap: ReadableMap?): HashMap<String, Any?>? {
        if (readableMap == null) {
            return null
        }

        val map: HashMap<String, Any?> = HashMap()
        val iterator = readableMap.keySetIterator()

        while (iterator.hasNextKey()) {
            val key = iterator.nextKey()
            val type = readableMap.getType(key)

            when (type) {
                ReadableType.Null -> map[key] = null
                ReadableType.Boolean -> map[key] = readableMap.getBoolean(key)
                ReadableType.Number -> {
                    val value = readableMap.getDouble(key)
                    // 检查 double 是否为整数
                    if (value == value.toLong().toDouble()) {
                        // 存为 Long
                        map[key] = value.toLong()
                    } else {
                        // 存为 Double
                        map[key] = value
                    }
                }

                ReadableType.String -> map[key] = readableMap.getString(key)
                ReadableType.Map ->
                    map[key] = convertMap(readableMap.getMap(key))

                ReadableType.Array ->
                    map[key] = convertArray(readableMap.getArray(key))
            }
        }
        return map
    }

    private fun convertArray(readableArray: ReadableArray?): ArrayList<Any>? {
        if (readableArray == null) {
            return null
        }

        val list: ArrayList<Any> = ArrayList(readableArray.size())
        for (i in 0..<readableArray.size()) {
            val type = readableArray.getType(i)

            when (type) {
                ReadableType.Null -> {  }
                ReadableType.Boolean -> list.add(readableArray.getBoolean(i))
                ReadableType.Number -> {
                    val value = readableArray.getDouble(i)
                    if (value == value.toLong().toDouble()) {
                        list.add(value.toLong())
                    } else {
                        list.add(value)
                    }
                }
                ReadableType.String -> {
                    readableArray.getString(i)?.let { list.add(it) }
                }
                ReadableType.Map -> {
                    convertMap(readableArray.getMap(i))?.let { list.add(it) }
                }
                ReadableType.Array -> {
                    convertArray(readableArray.getArray(i))?.let { list.add(it) }
                }
            }
        }
        return list
    }
}
