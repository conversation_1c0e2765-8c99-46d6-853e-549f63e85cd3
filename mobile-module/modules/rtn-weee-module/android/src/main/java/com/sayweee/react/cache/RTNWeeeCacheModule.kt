package com.sayweee.react.cache

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.annotations.ReactModule
import com.sayweee.core.order.OrderProvider
import com.sayweee.react.NativeRTNWeeeCacheSpec


//
// Created by <PERSON><PERSON> on 10/07/2025.
//
@ReactModule(name = RTNWeeeCacheModule.NAME)
class RTNWeeeCacheModule(reactContext: ReactApplicationContext): NativeRTNWeeeCacheSpec(reactContext) {


    override fun zipcode(): String {
        return OrderProvider.get().zipCode
    }

    override fun deliveryDate(): String {
        return OrderProvider.get().deliveryPickupDate
    }


    companion object {
        const val NAME = "RTNWeeeCache"
    }
}