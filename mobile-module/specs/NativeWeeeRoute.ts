import type {TurboModule} from 'react-native';
import {TurboModuleRegistry} from 'react-native';

import type {EventEmitter} from 'react-native/Libraries/Types/CodegenTypes';

type ProductParams = {
    product_id: string;
};

export type CustomEventPayload = {
  eventName: string;
  value: number;
};


export interface Spec extends TurboModule {
    navigateTo(url: string): void;

    registerCallback(onDataReceived: (data: string) => void): void;

    showMessage(message: string): void

    getBearerToken(): string

    getProduct(data: ProductParams): string

    readonly onCustomEvent: EventEmitter<CustomEventPayload>;
    

}

export default TurboModuleRegistry.getEnforcing<Spec>('WeeeRouteModule');
