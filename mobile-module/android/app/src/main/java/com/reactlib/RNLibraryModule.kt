package com.reactlib

import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod
import com.sayweee.rn.specs.NativeAwesomeSpec


//
// Created by <PERSON><PERSON> on 18/06/2025.
//
class RNLibraryModule(appContext: ReactApplicationContext): NativeAwesomeSpec(appContext) {


    companion object {
        const val NAME = "MyRNLibrary"
    }

    override fun getTypedExportedConstants(): Map<String, Any> {
        return mapOf("aa" to "Hello from Native AAR!")

    }

    override fun performAsyncTask(param: String?, promise: Promise?) {
        Thread {
            try {
                Thread.sleep(1000)
                promise?.resolve(true)
            } catch (e: Exception) {
                promise?.reject("E_ASYNC_ERROR", e)
            }
        }.start()
    }


}