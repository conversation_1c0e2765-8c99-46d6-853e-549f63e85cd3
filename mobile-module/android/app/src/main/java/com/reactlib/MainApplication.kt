package com.reactlib

import android.app.Application
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.sayweee.weee.rn.ReactNavApplication
import com.sayweee.weee.rn.ReactNavHelper

class MainApplication : Application(), ReactNavApplication {

  override val reactNativeHost: ReactNativeHost = ReactNavHelper.reactNativeHost(this)

  override val reactHost: ReactHost
    get() = ReactNavHelper.getDefaultReactHost(this, reactNativeHost)

  override fun onCreate() {
    super.onCreate()
//    loadReactNative(this)

    ReactNavHelper.onCreate(this)

  }
}
