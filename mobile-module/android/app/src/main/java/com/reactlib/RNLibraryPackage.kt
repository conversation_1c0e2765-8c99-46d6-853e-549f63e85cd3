package com.reactlib

import com.facebook.react.BaseReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.model.ReactModuleInfo
import com.facebook.react.module.model.ReactModuleInfoProvider


//
// Created by <PERSON><PERSON> on 18/06/2025.
//
class RNLibraryPackage: BaseReactPackage() {

    override fun getModule(name: String, reactContext: ReactApplicationContext): NativeModule? {
        return if (name == RNLibraryModule.NAME) {
            RNLibraryModule(reactContext)
        } else {
            null
        }
    }

    override fun getReactModuleInfoProvider(): ReactModuleInfoProvider {
        return ReactModuleInfoProvider {
            mapOf(
                RNLibraryModule.NAME to ReactModuleInfo(
                    RNLibraryModule.NAME,
                    RNLibraryModule.NAME,
                    false, // canOverrideExistingModule
                    false, // needsEagerInit
                    // hasConstants
                    false, // isCxxModule
                    true   // isTurboModule
                )
            )
        }
    }
}