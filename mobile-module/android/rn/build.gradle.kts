//apply(plugin = "com.facebook.react")
plugins {
//    id("com.facebook.react")
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
}


//react {
//    /* Folders */
////    root = file("../../")
//
//    /* Autolinking */
//    autolinkLibrariesWithApp()
//}

val enableProguardInReleaseBuilds = false
val jscFlavor = "io.github.react-native-community:jsc-android:2026004.+"

android {
    ndkVersion = rootProject.extra["ndkVersion"].toString()
    buildToolsVersion = rootProject.extra["buildToolsVersion"].toString()
    compileSdk = rootProject.extra["compileSdkVersion"].toString().toInt()

    namespace = "com.reactlib.rn"

    defaultConfig {
        minSdk = rootProject.extra["minSdkVersion"].toString().toInt()
        targetSdk = rootProject.extra["targetSdkVersion"].toString().toInt()
    }

    buildTypes {
        release {
            isMinifyEnabled = enableProguardInReleaseBuilds
            proguardFiles(
                getDefaultProguardFile("proguard-android.txt"),
                "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

dependencies {
    implementation("com.facebook.react:react-android:0.80.0")

    if (project.extra["hermesEnabled"].toString().toBoolean()) {
        implementation("com.facebook.react:hermes-android:0.80.0")
    } else {
        implementation(jscFlavor)
    }

    implementation("androidx.core:core-ktx:1.16.0")
    implementation("androidx.appcompat:appcompat:1.7.1")
    implementation("com.google.android.material:material:1.12.0")
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.2.1")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")
}

//configure<com.facebook.react.ReactExtension> {
//    autolinkLibrariesWithApp()
//}
