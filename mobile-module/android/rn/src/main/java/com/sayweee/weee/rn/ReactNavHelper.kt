package com.sayweee.weee.rn

import android.app.Application
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.reactlib.rn.BuildConfig


//
// Created by <PERSON><PERSON> on 17/06/2025.
//
object ReactNavHelper {

    fun reactNativeHost(app: Application): ReactNativeHost =
        object : DefaultReactNativeHost(app) {
            override fun getPackages(): List<ReactPackage> =
                listOf()
//                PackageList(this).packages.apply {
                    // Packages that cannot be autolinked yet can be added manually here, for example:
                    // add(MyReactNativePackage())
//                }

            override fun getJSMainModuleName(): String = "index"

            override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

            override val isNewArchEnabled: Boolean = true
            override val isHermesEnabled: Boolean = true
        }

    fun onCreate(app: Application) {
        SoLoader.init(app, OpenSourceMergedSoMapping)
//    load()
    }

    fun getDefaultReactHost(app: Application, host: ReactNativeHost): ReactHost =
        DefaultReactHost.getDefaultReactHost(app, host)

}