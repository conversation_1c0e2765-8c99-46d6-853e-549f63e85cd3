import {
  CliConfigInterface,
  ReleaseHistoryInterface,
  ReleaseInfo,
} from "@bravemobile/react-native-code-push";
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import axios from 'axios';
import FormData from 'form-data';

// 服务器配置
const SERVER_BASE_URL = process.env.SERVER_BASE_URL || 'http://localhost:3010';

// 生成唯一标识符
const generateUniqueIdentifier = (platform: string, version: string, customSeed?: string): string => {
  const timestamp = Date.now().toString();
  const seed = customSeed || Math.random().toString();
  const data = `${platform}-${version}-${timestamp}-${seed}`;
  return crypto.createHash('md5').update(data).digest('hex').substring(0, 8);
};

// 导出生成标识符函数
export const generateIdentifier = (platform: string, version: string, customSeed?: string): string => {
  return generateUniqueIdentifier(platform, version, customSeed);
};

// 创建配置对象
const Config: CliConfigInterface = {
  bundleUploader: async (
    source: string,
    platform: "ios" | "android",
    identifier?: string,
  ): Promise<{downloadUrl: string}> => {
    console.log(`Uploading bundle from ${source} for ${platform}`);
    
    try {
      const formData = new FormData();
      formData.append('bundle', fs.createReadStream(source));
      
      const id = identifier || 'default';
      const uploadUrl = `${SERVER_BASE_URL}/upload/${platform}/${id}`;
      
      console.log(`Uploading to: ${uploadUrl}`);
      const response = await axios.post(uploadUrl, formData, {
        headers: formData.getHeaders()
      });
      
      console.log(`Bundle uploaded successfully. Download URL: ${response.data.downloadUrl}`);
      return { downloadUrl: response.data.downloadUrl };
    } catch (error) {
      console.error('Failed to upload bundle:', error);
      throw error;
    }
  },

  getReleaseHistory: async (
    targetBinaryVersion: string,
    platform: "ios" | "android",
    identifier?: string,
  ): Promise<ReleaseHistoryInterface> => {
    console.log(`Getting release history for ${platform} version ${targetBinaryVersion}`);
    
    try {
      const id = identifier || 'default';
      const historyUrl = `${SERVER_BASE_URL}/history/${platform}/${targetBinaryVersion}/${id}/release-history.json`;
      
      console.log(`Fetching from: ${historyUrl}`);
      const response = await axios.get(historyUrl);
      
      console.log(`Release history retrieved successfully for ${platform} version ${targetBinaryVersion}`);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        console.log(`No release history found for ${platform} version ${targetBinaryVersion}`);
      } else {
        console.error('Failed to get release history:', error);
      }
      return {
        currentPackage: null,
        previousPackages: []
      } as unknown as ReleaseHistoryInterface;
    }
  },

  setReleaseHistory: async (
    targetBinaryVersion: string,
    jsonFilePath: string,
    releaseInfo: ReleaseHistoryInterface,
    platform: "ios" | "android",
    identifier?: string,
  ): Promise<void> => {
    console.log(`Setting release history for ${platform} version ${targetBinaryVersion}`);
    
    try {
      const id = identifier || 'default';
      const historyUrl = `${SERVER_BASE_URL}/history/${platform}/${targetBinaryVersion}/${id}`;
      
      console.log(`Posting to: ${historyUrl}`);
      await axios.post(historyUrl, releaseInfo);
      
      console.log(`Release history updated successfully for ${platform} version ${targetBinaryVersion}`);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Failed to set release history:', error.response?.data || error.message);
      } else {
        console.error('Failed to set release history:', error);
      }
      throw error;
    }
  },
};

// 正确导出配置
module.exports = Config;