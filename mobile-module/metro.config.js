const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');


/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */

const config = {
    resolver: {
        alias: {
            '@': path.resolve(__dirname, 'src'),
            'moti/skeleton': path.resolve(__dirname,'node_modules/moti/skeleton/react-native-linear-gradient'),
        },
        // unstable_enablePackageExports: false,

    }
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
