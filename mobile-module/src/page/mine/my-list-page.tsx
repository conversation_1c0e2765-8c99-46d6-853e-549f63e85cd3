import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
} from 'react-native';

import { pop } from '@/route';

import MyListTabSegment from './page-segment';

const MyListPage = (props: any) => {
  const [isEditMode, setIsEditMode] = useState(false);

  const toggleEditable = () => {
    setIsEditMode(!isEditMode)
  };

  const renderHeader = () => (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={pop}>
          <Text style={styles.backButtonText}>‹</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My lists</Text>
        <TouchableOpacity onPress={toggleEditable}>
          {isEditMode ? (
            <Text style={styles.cancelButton}>Cancel</Text>
          ) : (
            <Image source={{ uri: 'https://img.icons8.com/?size=100&id=99933&format=png&color=000000' }} style={styles.deleteIcon} />
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
    
      <MyListTabSegment type={props.type} isEditable={isEditMode} />

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  safeArea: {
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: '#0A72BA',
    fontWeight: '300',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0A72BA',
  },
  cancelButton: {
    fontSize: 16,
    color: '#0A72BA',
  },
  deleteIcon: {
    width: 24,
    height: 24,
  },
});

export default MyListPage;