import React, { useState, useMemo, useCallback } from 'react';
import {
  Animated,
  View,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';

import {
  TabView,
  SceneMap,
  NavigationState,
  SceneRendererProps,
  Route,
} from 'react-native-tab-view';
import SegmentProduct from './segment-product';


type MyListTabSegmentProps = {
  type: string,
  isEditable: boolean;
}

export const MYLIST_TYPE = {
  watchlist: "watchlist",
  bought: "bought",
}

function MyListTabSegment(props: MyListTabSegmentProps) {

  const routes: Route[] = [
    { key: MYLIST_TYPE.watchlist, title: '我的收藏' },
    { key: MYLIST_TYPE.bought, title: '再次购买' },
  ];
  const initialIndex = routes.findIndex(route => route.key === props.type);
  const [index, setIndex] = useState<number>(initialIndex !== -1 ? initialIndex : 0);

  const handleIndexChange = (newIndex: number) => setIndex(newIndex);

  const renderTabBar = (
    props: SceneRendererProps & { navigationState: NavigationState<Route>; }
  ) => {
    const inputRange = props.navigationState.routes.map((_, i) => i);

    return (
      <View style={styles.tabBar}>
        {props.navigationState.routes.map((route, i) => {
          const opacity = props.position.interpolate({
            inputRange,
            outputRange: inputRange.map((inputIndex) => inputIndex === i ? 1 : 0.5
            ),
          });

          return (
            <TouchableOpacity
              key={route.key}
              style={styles.tabItem}
              onPress={() => setIndex(i)}>
              <Animated.Text style={[{ opacity }, styles.tabText]}>
                {route.title}
              </Animated.Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

   const renderScene = ({ route }: { route: Route }) => {
    return <SegmentProduct {...props} type={route.key} />;
  };

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      renderTabBar={renderTabBar}
      onIndexChange={handleIndexChange} />
  );
}

export default MyListTabSegment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    paddingTop: 0,
    backgroundColor: '#ffffff',
    
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
  },
  tabText: {
    color: '#00000',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
