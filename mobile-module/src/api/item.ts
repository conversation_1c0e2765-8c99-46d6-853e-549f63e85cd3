import { NativeModules } from 'react-native';
import { WeeeResponse } from 'rtn-weee-module';
import { PurchaseItemReq } from './model';
import { Int32 } from 'react-native/Libraries/Types/CodegenTypes';


const { RTNWeeeNetwork, RTNWeeeCache } = NativeModules;


/**
 * 我的收藏列表
 * @param params
 * @returns
 */
export const getFavoriteList = (
  params: {
    zipcode?: string;
    offset?: Int32;
    limit?: Int32;
    filter_sub_category?: string;
  },
): WeeeResponse => {
  const finalParams = {
    ...params,
    zipcode: params.zipcode ?? RTNWeeeCache.zipcode(),
  };
  return RTNWeeeNetwork.get(`/ec/item/me/favorites/v2/list`, finalParams);
};

/**
 * @param params 批量取消收藏
 */
export const deleteFavoriteList = (
  params?: {
    product_ids: number[] | string[];
    is_watch: boolean;
  }
): WeeeResponse => {
  return RTNWeeeNetwork.post('/ec/item/me/favorites/batch/watch', params);
};


/**
 * 我的曾经购买
 * @param params
 * @returns
 */
export const getBoughtList = (
  params: {
    zipcode?: string | number;
    offset?: number;
    limit?: number;
    filter_sub_category: string;
  },
): WeeeResponse => {
  return RTNWeeeNetwork.get(`/ec/item/v5/recommend/bought/catalogue`, params);
};

/**
 * 商品增删改
 * @param items
 * @param config
 */
export const purchaseNormalV3 = (params: PurchaseItemReq[]): WeeeResponse => {
  return RTNWeeeNetwork.put_batch('/ec/so/porder/items/v3', params);
  };