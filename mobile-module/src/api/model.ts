import { Int32 } from "react-native/Libraries/Types/CodegenTypes";


/**
 * 加购对象
 */
export type PurchaseItemReq = Partial<{
  /**商品Id */
  product_id: Int32;
  /**商品数量 */
  quantity: Int32;
  /**类型 */
  source: string;
  refer_type: string;
  /**配送时间 */
  delivery_date: string;
  /** deal_id  vender_id */
  refer_value: string;
  //store的key
  source_store?: string;
  product_key?: string;
  options?: Array<{ op_id: number; op_qty: number; offset: number }>;
  /** 加购时的new_source -- jsonString, 具体type见 AddCartNewSourceType*/
  new_source: string;
  is_alcohol?: boolean;
  is_mkpl?: boolean;
  is_pantry?: boolean;
  min_order_quantity?: number;
}>;

export type ProductBean = {
  id: number;
  product_key: string;
  name: string;
  sub_name: string;
  img: string;
  sold_count: number;
  sold_count_ui: string;
  remaining_count: number;
  max_order_quantity: number;
  min_order_quantity: number;
  price: number;
  member_price: number;
  feature: number;
  product_sales_feature: number;
  discount_percentage: string;
  base_price: number;
  // Volume Pricing
  volume_price_support: boolean;
  volume_price: number;
  volume_threshold: number;
  trade_in_price: number;
  category: string;
  is_limit_product: boolean;
  is_pantry: boolean;
  bought_times: string;
  view_link: string;
  sold_status: string;
  product_max_order_quantity: number;
  category_name: string;
  category_color: string;
  show_member_price: boolean;
  tag_list: string[];
  label_list: LabelListBean[];
  product_tag_list: EntranceTag[];
  img_urls: string[];
  activity_tag_list: string[];
  is_hotdish: string;
  brand_name: string;
  brand_key: string;
  brand_link_url: string;
  everyday_low_price: boolean;
  item_type: string;
  post_count: number;
  post_count_ui: string;
  restock_tip: string;
  is_colding_package: boolean;
  is_mkpl: boolean;
  seller_id: string;
  vender_info_view: VenderInfo;
  recommendation_trace_id: string;
  relate_product: ProductBean[];
  media_urls: MediaBean[];
  prod_pos: number;
  is_sponsored: boolean;
  is_manual: boolean;
  sponsored_text: string;
  barInfoModules: BarInfoModules[];
  free_sample: boolean;
  is_presale: boolean;
  delivery_desc: string;
  sale_event_id: number | null;
  /*
   * Search v2 fields
   */
  is_search_v2: boolean;
  img_b64: string;
  img_filter: string;
  img_cached: boolean;
  ads_creative: AdsCreativeBean;
  entrance_tag: EntranceTag;
  selling_points: string[];
  member_support: boolean;
  is_cart_select: boolean;
  parent_category: string;
  category_name_son: string;
  biz_type: string;
  is_in_store_price: boolean;
  topRankingScore: number;
  extra_attrs: string;
  product_type: string;


  // local
  isSelected: boolean;
}

export type LabelListBean = {
  label_name: string;
  label_color: string;
  label_position: string;
  label_key: string;
  label_font_color: string;
  label_icon_url: string;
}

export type EntranceTag = {
  tag_name: string;
  tag_color: string;
  tag_position: string;
  tag_key: string;
  tag_font_color: string;
  tag_icon_url: string;
}

export type VenderInfo = {
  vender_id: number;
  vender_name: string;
  vender_logo_url: string;
  delivery_desc: string;
  free_delivery_desc: string;
  eta_range: string;
  promotion_infos: {};
  descriptions: {};
  overall_rating: number;
  sales_volume: string;
}

export type MediaBean = {
  media_type: string;
  media_url: string;
}

export type BarInfoModules = {
  module_type: string;
  module_data: string;
}

export type AdsCreativeBean = {
  creative_id: string;
  creative_type: string;
  creative_data: string;
}

